---
import { Image } from "astro:assets";
import type { WebmentionsChildren } from "@/types";
import { Icon } from "astro-icon/components";

interface Props {
	mentions: WebmentionsChildren[];
}

const { mentions } = Astro.props;

const validComments = ["mention-of", "in-reply-to"];

const comments = mentions.filter(
	(mention) => validComments.includes(mention["wm-property"]) && mention.content?.text,
);
---

{
	!!comments.length && (
		<div>
			<p class="text-accent-2 mb-0">
				<strong>{comments.length}</strong> Mention{comments.length > 1 ? "s" : ""}
			</p>
			<ul class="divide-global-text/20 mt-0 divide-y ps-0" role="list">
				{comments.map((mention) => (
					<li class="p-comment h-cite my-0 flex items-start gap-x-5 py-5">
						{mention.author?.photo && mention.author.photo !== "" ? (
							mention.author.url && mention.author.url !== "" ? (
								<a
									class="u-author not-prose ring-global-text hover:ring-link focus-visible:ring-link shrink-0 overflow-hidden rounded-full ring-2 hover:ring-4 focus-visible:ring-4"
									href={mention.author.url}
									rel="noreferrer"
									target="_blank"
									title={mention.author.name}
								>
									<Image
										alt={mention.author?.name}
										class="u-photo my-0 h-12 w-12"
										height={48}
										src={mention.author?.photo}
										width={48}
									/>
								</a>
							) : (
								<Image
									alt={mention.author?.name}
									class="u-photo my-0 h-12 w-12 rounded-full"
									height={48}
									src={mention.author?.photo}
									width={48}
								/>
							)
						) : null}
						<div class="flex-auto">
							<div class="p-author h-card flex items-center justify-between gap-x-2">
								<p class="p-name text-accent-2 my-0 line-clamp-1 font-semibold">
									{mention.author?.name}
								</p>
								<a
									aria-labelledby="cmt-source"
									class="u-url not-prose hover:text-link"
									href={mention.url}
									rel="noreferrer"
									target="_blank"
								>
									<span class="hidden" id="cmt-source">
										Visit the source of this webmention
									</span>
									<Icon
										aria-hidden="true"
										class="h-5 w-5"
										focusable="false"
										name="mdi:open-in-new"
									/>
								</a>
							</div>
							<p class="comment-content mt-1 mb-0 break-words [word-break:break-word]">
								{mention.content?.text}
							</p>
						</div>
					</li>
				))}
			</ul>
		</div>
	)
}
