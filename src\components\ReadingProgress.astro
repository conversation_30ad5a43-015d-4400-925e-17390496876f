---
// Reading Progress Bar Component
// Shows reading progress at the top of the page
---

<div id="reading-progress-container" class="reading-progress-container">
	<div id="reading-progress-bar" class="reading-progress-bar"></div>
</div>

<style>
	.reading-progress-container {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 3px;
		background-color: rgba(0, 0, 0, 0.05);
		z-index: 1000;
		opacity: 0;
		transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		backdrop-filter: blur(8px);
		-webkit-backdrop-filter: blur(8px);
	}

	.reading-progress-container.visible {
		opacity: 1;
	}

	.reading-progress-bar {
		height: 100%;
		width: 0%;
		background: oklch(65% 0.15 205);
		transition: width 0.15s cubic-bezier(0.4, 0, 0.2, 1);
		border-radius: 0 2px 2px 0;
	}

	/* Dark mode support */
	:global([data-theme="dark"]) .reading-progress-container {
		background-color: rgba(255, 255, 255, 0.05);
	}

	/* Responsive adjustments */
	@media (max-width: 640px) {
		.reading-progress-container {
			height: 2px;
		}
	}

	/* Reduce motion for users who prefer it */
	@media (prefers-reduced-motion: reduce) {
		.reading-progress-container {
			transition: opacity 0.1s linear;
		}

		.reading-progress-bar {
			transition: width 0.05s linear;
		}
	}

	/* High contrast mode support */
	@media (prefers-contrast: high) {
		.reading-progress-container {
			background-color: rgba(0, 0, 0, 0.2);
		}

		.reading-progress-bar {
			background: #000;
		}

		:global([data-theme="dark"]) .reading-progress-container {
			background-color: rgba(255, 255, 255, 0.2);
		}

		:global([data-theme="dark"]) .reading-progress-bar {
			background: #fff;
		}
	}
</style>

<script>
	class ReadingProgress extends HTMLElement {
		private progressBar: HTMLElement | null = null;
		private progressContainer: HTMLElement | null = null;
		private article: HTMLElement | null = null;
		private isVisible = false;
		private ticking = false;

		constructor() {
			super();
		}

		connectedCallback() {
			this.init();
		}

		private init() {
			// Get DOM elements
			this.progressBar = document.getElementById("reading-progress-bar");
			this.progressContainer = document.getElementById(
				"reading-progress-container",
			);
			this.article = document.querySelector(
				"article[data-pagefind-body]",
			);

			if (!this.progressBar || !this.progressContainer || !this.article) {
				console.warn("Reading Progress: Required elements not found");
				return;
			}

			// Bind scroll event with throttling
			this.bindScrollEvent();
		}

		private bindScrollEvent() {
			window.addEventListener(
				"scroll",
				() => {
					if (!this.ticking) {
						requestAnimationFrame(() => {
							this.updateProgress();
							this.ticking = false;
						});
						this.ticking = true;
					}
				},
				{ passive: true },
			);

			// Initial check
			this.updateProgress();
		}

		private updateProgress() {
			if (!this.article || !this.progressBar || !this.progressContainer)
				return;

			const windowHeight = window.innerHeight;
			const scrollTop =
				window.pageYOffset || document.documentElement.scrollTop;

			// Get article position relative to document
			const articleRect = this.article.getBoundingClientRect();
			const articleTop = articleRect.top + scrollTop;
			const articleHeight = this.article.offsetHeight;
			const articleBottom = articleTop + articleHeight;

			// Show progress bar when we start reading the article
			// Start showing slightly before reaching the article
			const shouldShow = scrollTop > articleTop - windowHeight * 0.2;

			if (shouldShow !== this.isVisible) {
				this.isVisible = shouldShow;
				this.progressContainer.classList.toggle("visible", shouldShow);
			}

			if (shouldShow) {
				// Calculate reading progress based on article content
				const articleStart = articleTop;
				const currentPosition = scrollTop + windowHeight; // Current bottom of viewport

				let progress = 0;

				if (currentPosition <= articleStart + windowHeight) {
					progress = 0;
				} else if (currentPosition >= articleBottom) {
					// When viewport bottom reaches article bottom, we're at 100%
					progress = 100;
				} else {
					// Linear interpolation between start and end
					const totalDistance =
						articleBottom - (articleStart + windowHeight);
					const currentDistance =
						currentPosition - (articleStart + windowHeight);
					progress = (currentDistance / totalDistance) * 100;
				}

				// Ensure progress is between 0 and 100
				progress = Math.max(0, Math.min(100, progress));

				// Update progress bar width with smooth transition
				this.progressBar.style.width = `${progress.toFixed(1)}%`;
			}
		}
	}

	// Register the custom element
	customElements.define("reading-progress", ReadingProgress);
</script>

<reading-progress></reading-progress>
