---
// Giscus 评论组件
// 基于 GitHub Discussions 的评论系统

interface Props {
	repo: string;
	repoId: string;
	category: string;
	categoryId: string;
	mapping?: string;
	strict?: string;
	reactionsEnabled?: string;
	emitMetadata?: string;
	inputPosition?: string;
	theme?: string;
	lang?: string;
	loading?: string;
}

const {
	repo,
	repoId,
	category,
	categoryId,
	mapping = "pathname",
	strict = "0",
	reactionsEnabled = "1",
	emitMetadata = "0",
	inputPosition = "bottom",
	theme = "light", // 默认使用 light，会被 JavaScript 动态更新
	lang = "zh-CN",
	loading = "lazy",
} = Astro.props;

// 导入 Giscus 样式
import "@/styles/giscus.css";
---

<section class="mt-16">
	<hr class="border-dashed mb-8" />
	<h6 class="title mb-6 text-xl before:hidden">评论</h6>
	<script
		id="giscus-script"
		src="https://giscus.app/client.js"
		data-repo={repo}
		data-repo-id={repoId}
		data-category={category}
		data-category-id={categoryId}
		data-mapping={mapping}
		data-strict={strict}
		data-reactions-enabled={reactionsEnabled}
		data-emit-metadata={emitMetadata}
		data-input-position={inputPosition}
		data-theme={theme}
		data-lang={lang}
		data-loading={loading}
		crossorigin="anonymous"
		async></script>
</section>

<script is:inline>
	// 在 Giscus 脚本加载之前就设置正确的主题
	(function () {
		// 获取当前主题
		function getCurrentTheme() {
			const storedTheme =
				typeof localStorage !== "undefined" &&
				localStorage.getItem("theme");
			const systemTheme = window.matchMedia(
				"(prefers-color-scheme: dark)",
			).matches
				? "dark"
				: "light";
			return storedTheme || systemTheme;
		}

		// 设置 Giscus 脚本的主题属性
		function setGiscusScriptTheme() {
			const giscusScript = document.getElementById("giscus-script");
			if (giscusScript) {
				const currentTheme = getCurrentTheme();
				const giscusTheme = currentTheme === "dark" ? "dark" : "light";
				giscusScript.setAttribute("data-theme", giscusTheme);
				console.log("Set initial Giscus theme to:", giscusTheme);
				return true;
			}
			return false;
		}

		// 立即设置主题，如果脚本还没加载则等待
		if (!setGiscusScriptTheme()) {
			// 脚本还没加载，使用 MutationObserver 监听
			const observer = new MutationObserver(function (mutations) {
				mutations.forEach(function (mutation) {
					mutation.addedNodes.forEach(function (node) {
						if (
							node.nodeType === 1 &&
							node.id === "giscus-script"
						) {
							setGiscusScriptTheme();
							observer.disconnect();
						}
					});
				});
			});
			observer.observe(document.body, { childList: true, subtree: true });
		}

		// 主题同步功能
		function updateGiscusTheme() {
			const iframe = document.querySelector("iframe.giscus-frame");
			if (!iframe) return;

			const theme = document.documentElement.getAttribute("data-theme");
			const giscusTheme = theme === "dark" ? "dark" : "light";

			if (iframe.contentWindow) {
				iframe.contentWindow.postMessage(
					{ giscus: { setConfig: { theme: giscusTheme } } },
					"https://giscus.app",
				);
				console.log("Updated Giscus theme to:", giscusTheme);
			}
		}

		// 监听主题变化事件
		document.addEventListener("theme-change", function (e) {
			console.log("Theme change detected:", e.detail.theme);
			setTimeout(function () {
				updateGiscusTheme();
			}, 100);
		});

		// 监听 Astro 页面切换
		document.addEventListener("astro:after-swap", function () {
			setTimeout(function () {
				setGiscusScriptTheme();
				updateGiscusTheme();
			}, 100);
		});

		// 页面加载完成后的处理
		if (document.readyState === "loading") {
			document.addEventListener("DOMContentLoaded", function () {
				setTimeout(updateGiscusTheme, 1000);
			});
		} else {
			setTimeout(updateGiscusTheme, 1000);
		}

		// 监听 Giscus 加载完成
		window.addEventListener("message", function (event) {
			if (event.origin !== "https://giscus.app") return;
			if (event.data.giscus && event.data.giscus.discussion) {
				console.log("Giscus loaded, syncing theme");
				setTimeout(updateGiscusTheme, 100);
			}
		});

		// 监听系统主题变化
		window
			.matchMedia("(prefers-color-scheme: dark)")
			.addEventListener("change", function () {
				if (!localStorage.getItem("theme")) {
					setTimeout(function () {
						setGiscusScriptTheme();
						updateGiscusTheme();
					}, 100);
				}
			});
	})();
</script>
