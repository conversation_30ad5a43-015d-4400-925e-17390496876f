{
	// Place your astro-cactus workspace snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and
	// description. Add comma separated ids of the languages where the snippet is applicable in the scope field. If scope
	// is left empty or omitted, the snippet gets applied to all languages. The prefix is what is
	// used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
	// $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders.
	// Placeholders with the same ids are connected.
	// Example:
	// "Print to console": {
	// 	"scope": "javascript,typescript",
	// 	"prefix": "log",
	// 	"body": [
	// 		"console.log('$1');",
	// 		"$2"
	// 	],
	// 	"description": "Log output to console"
	// }
	"Add frontmatter to an Astro Cactus Post": {
		"scope": "markdown,mdx",
		"prefix": "frontmatter-post",
		"body": [
			"---",
			"title: ${TM_FILENAME_BASE/(.*)/${1:/capitalize}/}",
			"description: 'Please enter a description of your post here, between 50-160 chars!'",
			"publishDate: $CURRENT_DATE $CURRENT_MONTH_NAME $CURRENT_YEAR",
			"tags: []",
			"draft: false",
			"---",
			"$2",
		],
		"description": "Add frontmatter for new Markdown post",
	},
	"Add frontmatter to an Astro Cactus Note": {
		"scope": "markdown,mdx",
		"prefix": "frontmatter-note",
		"body": [
			"---",
			"title: ${TM_FILENAME_BASE/(.*)/${1:/capitalize}/}",
			"description: 'Enter a description here (optional)'",
			"publishDate: \"${CURRENT_YEAR}-${CURRENT_MONTH}-${CURRENT_DATE}T${CURRENT_HOUR}:${CURRENT_MINUTE}:00Z\"",
			"---",
			"$2",
		],
		"description": "Add frontmatter for a new Markdown note",
	},
}
