---
import { Image } from "astro:assets";
import type { WebmentionsChildren } from "@/types";

interface Props {
	mentions: WebmentionsChildren[];
}

const { mentions } = Astro.props;
const MAX_LIKES = 10;

const likes = mentions.filter((mention) => mention["wm-property"] === "like-of");
const likesToShow = likes
	.filter((like) => like.author?.photo && like.author.photo !== "")
	.slice(0, MAX_LIKES);
---

{
	!!likes.length && (
		<div>
			<p class="text-accent-2 mb-0">
				<strong>{likes.length}</strong>
				{likes.length > 1 ? " People" : " Person"} liked this
			</p>
			{!!likesToShow.length && (
				<ul class="flex list-none flex-wrap overflow-hidden ps-2" role="list">
					{likesToShow.map((like) => (
						<li class="p-like h-cite -ms-2">
							<a
								class="u-url not-prose ring-global-text hover:ring-link focus-visible:ring-link relative inline-block overflow-hidden rounded-full ring-2 hover:z-10 hover:ring-4 focus-visible:z-10 focus-visible:ring-4"
								href={like.author?.url}
								rel="noreferrer"
								target="_blank"
								title={like.author?.name}
							>
								<span class="p-author h-card">
									<Image
										alt={like.author!.name}
										class="u-photo my-0 inline-block h-12 w-12"
										height={48}
										src={like.author!.photo}
										width={48}
									/>
								</span>
							</a>
						</li>
					))}
				</ul>
			)}
		</div>
	)
}
