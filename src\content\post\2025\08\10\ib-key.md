---
title: "IB Key认证失败问题解决"
description: "IB Key认证失败问题解决"
publishDate: "2025-08-10 02:12:48"
# updatedDate: 22 Jan 2024
draft: false
tags: ["交易"]
---

#### 启用IBKR双因素验证问题
IB Key在输入完PIN码之后,总是与服务器通讯发生错误。

#### 解决方法
直接说解决方法

1. 卸载手机上的IBKR
2. 访问 https://www.ibkrguides.com/android/sls/activating-two-factor-via-mobile.htm
3. 找到超链接 "For Clients in Mainland China"，点击后在打开的页面下载和安装适用于中国的客户端
4. 打开刚刚安装好的客户端,进行IB Key验证就没有问题
5. IB Key验证完成之后,卸载适用于中国的客户端，再下载和安装回原来的IBKR即可

所以，多看看官方文档,是可以解决问题的
