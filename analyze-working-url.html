<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analyze Working Google Maps URL</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1200px; margin: 0 auto; padding: 20px; }
        .section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        .url-display { background: #f5f5f5; padding: 10px; border-radius: 4px; word-break: break-all; font-size: 12px; }
        iframe { width: 100%; height: 300px; border: 0; border-radius: 4px; margin: 10px 0; }
        .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        pre { background: #f8f8f8; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>Google Maps URL Analysis</h1>
    
    <div class="section">
        <h2>🏔️ Working Example 1 (黄山)</h2>
        <div class="url-display" id="working-url-1"></div>
        <iframe id="working-iframe-1"></iframe>
    </div>
    
    <div class="section">
        <h2>🎭 Working Example 2 (The Avalon Theatre)</h2>
        <div class="url-display" id="working-url-2"></div>
        <iframe id="working-iframe-2"></iframe>
    </div>
    
    <div class="section">
        <h2>📊 PB Parameter Analysis</h2>
        <div id="analysis"></div>
    </div>
    
    <div class="section">
        <h2>🔧 Generate Similar URL</h2>
        <div>
            <label>Latitude: <input type="number" id="lat" value="39.9042" step="any"></label>
            <label>Longitude: <input type="number" id="lng" value="116.4074" step="any"></label>
            <label>Zoom: <input type="number" id="zoom" value="13" step="0.1"></label>
            <button onclick="generateUrl()">Generate URL</button>
        </div>
        <div class="url-display" id="generated-url"></div>
        <iframe id="generated-iframe"></iframe>
    </div>

    <script>
        // Working URLs
        const workingUrl1 = "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d212623.09168893012!2d117.68538765589432!3d30.17751277765749!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3435c68c09678d3d%3A0x706d912f1c61517e!2z6buE5bGx!5e0!3m2!1szh-CN!2s!4v1757992277370!5m2!1szh-CN!2s";
        
        const workingUrl2 = "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d6204.450648988351!2d-77.08006951319453!3d38.96452265412205!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x89b7c9a3b1d8124d%3A0x68ad77cf2b2381fd!2sThe%20Avalon%20Theatre!5e0!3m2!1szh-CN!2sus!4v1757989736646!5m2!1szh-CN!2sus";
        
        // Display URLs
        document.getElementById('working-url-1').textContent = workingUrl1;
        document.getElementById('working-url-2').textContent = workingUrl2;
        
        // Load iframes
        document.getElementById('working-iframe-1').src = workingUrl1;
        document.getElementById('working-iframe-2').src = workingUrl2;
        
        // Parse PB parameters
        function parsePbParameter(url) {
            const pbMatch = url.match(/pb=([^&]+)/);
            if (!pbMatch) return null;
            
            const pb = decodeURIComponent(pbMatch[1]);
            const parts = pb.split('!').filter(p => p.length > 0);
            
            // Extract key information
            const analysis = {};
            parts.forEach((part, index) => {
                if (part.startsWith('1d')) analysis.distance = parseFloat(part.substring(2));
                if (part.startsWith('2d')) analysis.longitude = parseFloat(part.substring(2));
                if (part.startsWith('3d')) analysis.latitude = parseFloat(part.substring(2));
                if (part.startsWith('4f')) analysis.zoom = parseFloat(part.substring(2));
                if (part.startsWith('1s0x')) analysis.placeId = part.substring(2);
                if (part.startsWith('2s') && !part.startsWith('2s!')) analysis.placeName = decodeURIComponent(part.substring(2));
                if (part.startsWith('4v')) analysis.timestamp = part.substring(2);
                if (part.startsWith('5e')) analysis.mapType = part.substring(2);
            });
            
            return { raw: pb, parts: parts, analysis: analysis };
        }
        
        const pb1 = parsePbParameter(workingUrl1);
        const pb2 = parsePbParameter(workingUrl2);
        
        // Display analysis
        document.getElementById('analysis').innerHTML = `
            <div class="grid">
                <div>
                    <h3>黄山 (Huangshan) Analysis:</h3>
                    <pre>${JSON.stringify(pb1.analysis, null, 2)}</pre>
                    <p><strong>Structure:</strong> ${pb1.parts.length} parts</p>
                    <p><strong>Key parts:</strong></p>
                    <ul>
                        <li>Distance: ${pb1.analysis.distance}</li>
                        <li>Coordinates: ${pb1.analysis.latitude}, ${pb1.analysis.longitude}</li>
                        <li>Zoom: ${pb1.analysis.zoom}</li>
                        <li>Place ID: ${pb1.analysis.placeId}</li>
                        <li>Place Name: ${pb1.analysis.placeName}</li>
                    </ul>
                </div>
                <div>
                    <h3>Avalon Theatre Analysis:</h3>
                    <pre>${JSON.stringify(pb2.analysis, null, 2)}</pre>
                    <p><strong>Structure:</strong> ${pb2.parts.length} parts</p>
                    <p><strong>Key parts:</strong></p>
                    <ul>
                        <li>Distance: ${pb2.analysis.distance}</li>
                        <li>Coordinates: ${pb2.analysis.latitude}, ${pb2.analysis.longitude}</li>
                        <li>Zoom: ${pb2.analysis.zoom}</li>
                        <li>Place ID: ${pb2.analysis.placeId}</li>
                        <li>Place Name: ${pb2.analysis.placeName}</li>
                    </ul>
                </div>
            </div>
            
            <h3>Common Pattern:</h3>
            <p>Both URLs follow the same structure:</p>
            <pre>!1m18!1m12!1m3!1d{distance}!2d{lng}!3d{lat}!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f{zoom}!3m3!1m2!1s{placeId}!2s{placeName}!5e{mapType}!3m2!1s{lang}!2s{region}!4v{timestamp}!5m2!1s{lang}!2s{region}</pre>
        `;
        
        // Generate URL function
        function generateUrl() {
            const lat = parseFloat(document.getElementById('lat').value);
            const lng = parseFloat(document.getElementById('lng').value);
            const zoom = parseFloat(document.getElementById('zoom').value);
            
            // Calculate distance using the same formula as Google Maps
            const distance = 156543.03392 * Math.cos(lat * Math.PI / 180) / Math.pow(2, zoom);
            
            // Generate a simple place ID (for coordinates, we can use a generic format)
            const timestamp = Date.now();
            const language = 'zh-CN';
            const region = 's'; // Note: working examples use 's' not 'us'
            
            // For coordinates without a specific place, we can use a generic place ID
            const placeId = '0x0:0x0';
            const placeName = `${lat},${lng}`;
            
            const pb = [
                '!1m18',
                '!1m12',
                '!1m3',
                `!1d${distance}`,
                `!2d${lng}`,
                `!3d${lat}`,
                '!2m3',
                '!1f0',
                '!2f0',
                '!3f0',
                '!3m2',
                '!1i1024',
                '!2i768',
                `!4f${zoom}.1`,
                '!3m3',
                '!1m2',
                `!1s${placeId}`,
                `!2s${encodeURIComponent(placeName)}`,
                '!5e0',
                '!3m2',
                `!1s${language}`,
                `!2s${region}`,
                `!4v${timestamp}`,
                '!5m2',
                `!1s${language}`,
                `!2s${region}`
            ].join('');
            
            const url = `https://www.google.com/maps/embed?pb=${pb}`;
            
            document.getElementById('generated-url').textContent = url;
            document.getElementById('generated-iframe').src = url;
            
            console.log('Generated URL:', url);
        }
        
        // Generate initial URL
        generateUrl();
    </script>
</body>
</html>
